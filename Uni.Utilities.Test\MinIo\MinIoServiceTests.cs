using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using UNI.Utilities.MinIo;
using Xunit;

namespace UNI.Utilities.Test.MinIo
{
    public class MinIoServiceTests
    {
        private readonly Mock<IOptions<MinIoSettings>> _mockOptions;
        private readonly Mock<ILogger<MinIoService>> _mockLogger;
        private readonly MinIoSettings _settings;

        public MinIoServiceTests()
        {
            _settings = new MinIoSettings
            {
                Endpoint = "storage-dev.unicloudgroup.com.vn",
                AccessKey = "iZJWUfthMR4McKt6VvBO",
                SecretKey = "jUyIENNflJeHzMS4SH9qh5nWR7b6h47DMLaLiXcE",
                UseSSL = true,
                CreateBucketIfNotExists = true
            };

            _mockOptions = new Mock<IOptions<MinIoSettings>>();
            _mockOptions.Setup(x => x.Value).Returns(_settings);
            
            _mockLogger = new Mock<ILogger<MinIoService>>();
        }

        [Fact]
        public async Task UploadAndDownloadObject_ShouldWork()
        {
            // Arrange
            var service = new MinIoService(_mockOptions.Object, _mockLogger.Object);
            var bucketName = "test-bucket";
            var objectName = "test-file.txt";
            var content = "Hello, MinIO Test!";
            var contentBytes = Encoding.UTF8.GetBytes(content);

            try
            {
                // Act - Upload
                using var stream = new MemoryStream(contentBytes);
                var uploadResult = await service.UploadObjectAsync(bucketName, objectName, stream);
                
                // Assert upload result
                Assert.NotNull(uploadResult);
                Assert.Equal(bucketName, uploadResult.BucketName);
                Assert.Equal(objectName, uploadResult.ObjectName);
                Assert.Equal(contentBytes.Length, uploadResult.Size);
                
                // Act - Download
                var downloadedBytes = await service.GetObjectBytesAsync(bucketName, objectName);
                var downloadedContent = Encoding.UTF8.GetString(downloadedBytes);
                
                // Assert downloaded content
                Assert.Equal(content, downloadedContent);
                
                // Act - Check if exists
                var exists = await service.ObjectExistsAsync(bucketName, objectName);
                
                // Assert exists
                Assert.True(exists);
                
                // Act - Get object info
                var objectInfo = await service.GetObjectInfoAsync(bucketName, objectName);
                
                // Assert object info
                Assert.NotNull(objectInfo);
                Assert.Equal(objectName, objectInfo.ObjectName);
                Assert.Equal(contentBytes.Length, objectInfo.Size);
            }
            finally
            {
                // Cleanup
                await service.DeleteObjectAsync(bucketName, objectName);
            }
        }

        [Fact]
        public async Task BucketOperations_ShouldWork()
        {
            // Arrange
            var service = new MinIoService(_mockOptions.Object, _mockLogger.Object);
            var bucketName = "test-bucket-operations";

            try
            {
                // Act - Create bucket
                await service.CreateBucketAsync(bucketName);
                
                // Act - Check if exists
                var exists = await service.BucketExistsAsync(bucketName);
                
                // Assert exists
                Assert.True(exists);
                
                // Act - List buckets
                var buckets = await service.ListBucketsAsync();
                
                // Assert buckets
                Assert.Contains(buckets, b => b.Name == bucketName);
            }
            finally
            {
                // Cleanup
                await service.DeleteBucketAsync(bucketName);
            }
        }

        [Fact]
        public async Task PresignedUrls_ShouldBeGenerated()
        {
            // Arrange
            var service = new MinIoService(_mockOptions.Object, _mockLogger.Object);
            var bucketName = "test-presigned-urls";
            var objectName = "test-presigned.txt";
            var content = "Presigned URL Test";
            var contentBytes = Encoding.UTF8.GetBytes(content);

            try
            {
                // Setup
                if (!await service.BucketExistsAsync(bucketName))
                {
                    await service.CreateBucketAsync(bucketName);
                }

                using var stream = new MemoryStream(contentBytes);
                await service.UploadObjectAsync(bucketName, objectName, stream);

                // Act - Generate URLs
                var downloadUrl = await service.GetPresignedDownloadUrlAsync(bucketName, objectName, TimeSpan.FromMinutes(5));
                var previewUrl = await service.GetPreviewUrlAsync(bucketName, objectName, TimeSpan.FromMinutes(5));
                var uploadUrl = await service.GetPresignedUploadUrlAsync(bucketName, "new-" + objectName, TimeSpan.FromMinutes(5));

                // Assert URLs
                Assert.NotNull(downloadUrl);
                Assert.NotNull(previewUrl);
                Assert.NotNull(uploadUrl);
                Assert.Contains(bucketName, downloadUrl);
                Assert.Contains(objectName, downloadUrl);
                Assert.Contains(bucketName, previewUrl);
                Assert.Contains(objectName, previewUrl);
                Assert.Contains(bucketName, uploadUrl);
                Assert.Contains("new-" + objectName, uploadUrl);
            }
            finally
            {
                // Cleanup
                await service.DeleteObjectAsync(bucketName, objectName);
                await service.DeleteBucketAsync(bucketName);
            }
        }

        [Fact]
        public async Task DownloadOperations_ShouldWork()
        {
            // Arrange
            var service = new MinIoService(_mockOptions.Object, _mockLogger.Object);
            var bucketName = "test-download";
            var objectName = "test-download.txt";
            var content = "Download Test Content";
            var contentBytes = Encoding.UTF8.GetBytes(content);

            try
            {
                // Setup
                if (!await service.BucketExistsAsync(bucketName))
                {
                    await service.CreateBucketAsync(bucketName);
                }

                using var uploadStream = new MemoryStream(contentBytes);
                await service.UploadObjectAsync(bucketName, objectName, uploadStream);

                // Act - Download to stream
                using var downloadStream = new MemoryStream();
                await service.DownloadObjectAsync(bucketName, objectName, downloadStream);
                var downloadedContent = Encoding.UTF8.GetString(downloadStream.ToArray());

                // Assert stream download
                Assert.Equal(content, downloadedContent);

                // Act - Download to file
                var tempFilePath = Path.GetTempFileName();
                try
                {
                    await service.DownloadFileAsync(bucketName, objectName, tempFilePath);
                    var fileContent = await File.ReadAllTextAsync(tempFilePath);

                    // Assert file download
                    Assert.Equal(content, fileContent);
                }
                finally
                {
                    if (File.Exists(tempFilePath))
                        File.Delete(tempFilePath);
                }
            }
            finally
            {
                // Cleanup
                await service.DeleteObjectAsync(bucketName, objectName);
                await service.DeleteBucketAsync(bucketName);
            }
        }

        [Fact]
        public async Task LargeFileUpload_ShouldWork()
        {
            // Arrange
            var service = new MinIoService(_mockOptions.Object, _mockLogger.Object);
            var bucketName = "test-large-upload";
            var objectName = "large-test-file.bin";

            // Create a 10MB test file
            var largeFileSize = 10 * 1024 * 1024; // 10MB
            var largeFileData = new byte[largeFileSize];
            new Random().NextBytes(largeFileData);

            try
            {
                // Setup
                if (!await service.BucketExistsAsync(bucketName))
                {
                    await service.CreateBucketAsync(bucketName);
                }

                // Act - Upload large object from stream with progress tracking
                var progressReports = new List<UploadProgress>();
                var progress = new Progress<UploadProgress>(p => progressReports.Add(p));

                using var stream = new MemoryStream(largeFileData);
                var uploadResult = await service.UploadLargeObjectAsync(
                    bucketName,
                    objectName,
                    stream,
                    largeFileSize,
                    partSize: 5 * 1024 * 1024, // 5MB parts
                    progressCallback: progress);

                // Assert upload result
                Assert.NotNull(uploadResult);
                Assert.Equal(bucketName, uploadResult.BucketName);
                Assert.Equal(objectName, uploadResult.ObjectName);
                Assert.Equal(largeFileSize, uploadResult.Size);

                // Assert progress was reported
                Assert.NotEmpty(progressReports);
                Assert.True(progressReports.Any(p => p.PercentageComplete > 0));

                // Verify the uploaded file
                var exists = await service.ObjectExistsAsync(bucketName, objectName);
                Assert.True(exists);

                var objectInfo = await service.GetObjectInfoAsync(bucketName, objectName);
                Assert.Equal(largeFileSize, objectInfo.Size);
            }
            finally
            {
                // Cleanup
                await service.DeleteObjectAsync(bucketName, objectName);
                await service.DeleteBucketAsync(bucketName);
            }
        }

        [Fact]
        public async Task LargeFileUploadFromFile_ShouldWork()
        {
            // Arrange
            var service = new MinIoService(_mockOptions.Object, _mockLogger.Object);
            var bucketName = "test-large-file-upload";
            var objectName = "large-file-test.bin";

            // Create a temporary large file
            var tempFilePath = Path.GetTempFileName();
            var largeFileSize = 8 * 1024 * 1024; // 8MB
            var largeFileData = new byte[largeFileSize];
            new Random().NextBytes(largeFileData);

            try
            {
                await File.WriteAllBytesAsync(tempFilePath, largeFileData);

                // Setup
                if (!await service.BucketExistsAsync(bucketName))
                {
                    await service.CreateBucketAsync(bucketName);
                }

                // Act - Upload large file with progress tracking
                var progressReports = new List<UploadProgress>();
                var progress = new Progress<UploadProgress>(p => progressReports.Add(p));

                var uploadResult = await service.UploadLargeFileAsync(
                    bucketName,
                    objectName,
                    tempFilePath,
                    partSize: 4 * 1024 * 1024, // 4MB parts
                    progressCallback: progress);

                // Assert upload result
                Assert.NotNull(uploadResult);
                Assert.Equal(bucketName, uploadResult.BucketName);
                Assert.Equal(objectName, uploadResult.ObjectName);
                Assert.Equal(largeFileSize, uploadResult.Size);

                // Assert progress was reported
                Assert.NotEmpty(progressReports);

                // Verify the uploaded file
                var exists = await service.ObjectExistsAsync(bucketName, objectName);
                Assert.True(exists);
            }
            finally
            {
                // Cleanup
                if (File.Exists(tempFilePath))
                    File.Delete(tempFilePath);

                await service.DeleteObjectAsync(bucketName, objectName);
                await service.DeleteBucketAsync(bucketName);
            }
        }

        [Fact]
        public async Task BatchDeleteObjects_ShouldWork()
        {
            // Arrange
            var service = new MinIoService(_mockOptions.Object, _mockLogger.Object);
            var bucketName = "test-batch-delete";
            var objectNames = new[] { "file1.txt", "file2.txt", "file3.txt" };
            var content = "Test content for batch delete";
            var contentBytes = Encoding.UTF8.GetBytes(content);

            try
            {
                // Setup
                if (!await service.BucketExistsAsync(bucketName))
                {
                    await service.CreateBucketAsync(bucketName);
                }

                // Upload multiple objects
                foreach (var objectName in objectNames)
                {
                    using var stream = new MemoryStream(contentBytes);
                    await service.UploadObjectAsync(bucketName, objectName, stream);
                }

                // Verify all objects exist
                foreach (var objectName in objectNames)
                {
                    var exists = await service.ObjectExistsAsync(bucketName, objectName);
                    Assert.True(exists);
                }

                // Act - Batch delete
                var deleteResults = await service.DeleteObjectsAsync(bucketName, objectNames);

                // Assert delete results
                Assert.NotNull(deleteResults);
                Assert.Equal(objectNames.Length, deleteResults.Count());

                foreach (var result in deleteResults)
                {
                    Assert.True(result.IsSuccess);
                    Assert.Contains(result.ObjectName, objectNames);
                }

                // Verify all objects are deleted
                foreach (var objectName in objectNames)
                {
                    var exists = await service.ObjectExistsAsync(bucketName, objectName);
                    Assert.False(exists);
                }
            }
            finally
            {
                // Cleanup
                await service.DeleteBucketAsync(bucketName);
            }
        }

        [Fact]
        public async Task ListObjects_ShouldWork()
        {
            // Arrange
            var service = new MinIoService(_mockOptions.Object, _mockLogger.Object);
            var bucketName = "test-list-objects";
            var objectNames = new[] { "folder1/file1.txt", "folder1/file2.txt", "folder2/file3.txt", "root-file.txt" };
            var content = "Test content for listing";
            var contentBytes = Encoding.UTF8.GetBytes(content);

            try
            {
                // Setup
                if (!await service.BucketExistsAsync(bucketName))
                {
                    await service.CreateBucketAsync(bucketName);
                }

                // Upload multiple objects
                foreach (var objectName in objectNames)
                {
                    using var stream = new MemoryStream(contentBytes);
                    await service.UploadObjectAsync(bucketName, objectName, stream);
                }

                // Act - List all objects
                var allObjects = await service.ListObjectsAsync(bucketName);

                // Assert all objects
                Assert.NotNull(allObjects);
                Assert.Equal(objectNames.Length, allObjects.Count());

                foreach (var objectName in objectNames)
                {
                    Assert.Contains(allObjects, obj => obj.ObjectName == objectName);
                }

                // Act - List objects with prefix
                var folder1Objects = await service.ListObjectsAsync(bucketName, "folder1/");

                // Assert filtered objects
                Assert.NotNull(folder1Objects);
                Assert.Equal(2, folder1Objects.Count());
                Assert.All(folder1Objects, obj => Assert.StartsWith("folder1/", obj.ObjectName));

                // Act - List objects recursively
                var recursiveObjects = await service.ListObjectsAsync(bucketName, recursive: true);

                // Assert recursive listing
                Assert.NotNull(recursiveObjects);
                Assert.Equal(objectNames.Length, recursiveObjects.Count());
            }
            finally
            {
                // Cleanup
                foreach (var objectName in objectNames)
                {
                    await service.DeleteObjectAsync(bucketName, objectName);
                }
                await service.DeleteBucketAsync(bucketName);
            }
        }

        [Fact]
        public async Task ProxyEndpointConfiguration_ShouldReplaceUrlsCorrectly()
        {
            // Arrange
            var proxySettings = new MinIoSettings
            {
                Endpoint = "localhost:9000",
                ProxyEndpoint = "https://files.example.com",
                AccessKey = "minioadmin",
                SecretKey = "minioadmin",
                UseSSL = false,
                CreateBucketIfNotExists = true
            };

            var mockProxyOptions = new Mock<IOptions<MinIoSettings>>();
            mockProxyOptions.Setup(x => x.Value).Returns(proxySettings);

            var service = new MinIoService(mockProxyOptions.Object, _mockLogger.Object);
            var bucketName = "test-proxy-endpoint";
            var objectName = "test-proxy.txt";
            var content = "Proxy endpoint test";
            var contentBytes = Encoding.UTF8.GetBytes(content);

            try
            {
                // Setup
                if (!await service.BucketExistsAsync(bucketName))
                {
                    await service.CreateBucketAsync(bucketName);
                }

                using var stream = new MemoryStream(contentBytes);
                await service.UploadObjectAsync(bucketName, objectName, stream);

                // Act - Generate URLs with proxy endpoint
                var downloadUrl = await service.GetPresignedDownloadUrlAsync(bucketName, objectName, TimeSpan.FromMinutes(5));
                var previewUrl = await service.GetPreviewUrlAsync(bucketName, objectName, TimeSpan.FromMinutes(5));
                var uploadUrl = await service.GetPresignedUploadUrlAsync(bucketName, "new-" + objectName, TimeSpan.FromMinutes(5));

                // Assert URLs use proxy endpoint
                Assert.NotNull(downloadUrl);
                Assert.NotNull(previewUrl);
                Assert.NotNull(uploadUrl);

                Assert.Contains("files.example.com", downloadUrl);
                Assert.Contains("files.example.com", previewUrl);
                Assert.Contains("files.example.com", uploadUrl);

                // URLs should not contain the original endpoint
                Assert.DoesNotContain("localhost:9000", downloadUrl);
                Assert.DoesNotContain("localhost:9000", previewUrl);
                Assert.DoesNotContain("localhost:9000", uploadUrl);
            }
            finally
            {
                // Cleanup
                await service.DeleteObjectAsync(bucketName, objectName);
                await service.DeleteBucketAsync(bucketName);
            }
        }

        #region Unit Tests (No MinIO Server Required)

        [Fact]
        public void MinIoService_Constructor_ShouldThrowNullReferenceException_WhenOptionsIsNull()
        {
            // Act & Assert
            Assert.Throws<NullReferenceException>(() => new MinIoService((IOptions<MinIoSettings>)null!, _mockLogger.Object));
        }

        [Fact]
        public void MinIoService_Constructor_ShouldThrowArgumentNullException_WhenSettingsIsNull()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => new MinIoService((MinIoSettings)null!, _mockLogger.Object));
        }

        [Fact]
        public void MinIoService_Constructor_ShouldThrowArgumentNullException_WhenLoggerIsNull()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => new MinIoService(_mockOptions.Object, null!));
        }

        [Fact]
        public void MinIoService_Constructor_ShouldCreateInstance_WithValidParameters()
        {
            // Act
            var service = new MinIoService(_mockOptions.Object, _mockLogger.Object);

            // Assert
            Assert.NotNull(service);
        }

        [Fact]
        public async Task UploadFileAsync_ShouldThrowMinIoException_WhenFileDoesNotExist()
        {
            // Arrange
            var service = new MinIoService(_mockOptions.Object, _mockLogger.Object);
            var nonExistentFilePath = "non-existent-file.txt";

            // Act & Assert
            var exception = await Assert.ThrowsAsync<MinIoException>(() =>
                service.UploadFileAsync("test-bucket", "test-object", nonExistentFilePath));

            Assert.Contains("File not found", exception.Message);
        }

        [Fact]
        public async Task UploadLargeFileAsync_ShouldThrowMinIoException_WhenFileDoesNotExist()
        {
            // Arrange
            var service = new MinIoService(_mockOptions.Object, _mockLogger.Object);
            var nonExistentFilePath = "non-existent-large-file.txt";

            // Act & Assert
            var exception = await Assert.ThrowsAsync<MinIoException>(() =>
                service.UploadLargeFileAsync("test-bucket", "test-object", nonExistentFilePath));

            Assert.Contains("File not found", exception.Message);
        }

        [Theory]
        [InlineData("")]
        [InlineData(null)]
        public async Task BucketExistsAsync_ShouldThrowMinIoException_WhenBucketNameIsNullOrEmpty(string bucketName)
        {
            // Arrange
            var service = new MinIoService(_mockOptions.Object, _mockLogger.Object);

            // Act & Assert
            var exception = await Assert.ThrowsAsync<MinIoException>(() => service.BucketExistsAsync(bucketName));
            Assert.Contains("Bucket name cannot be empty", exception.Message);
        }

        [Theory]
        [InlineData("")]
        [InlineData(null)]
        public async Task ObjectExistsAsync_ShouldThrowMinIoException_WhenBucketNameIsNullOrEmpty(string bucketName)
        {
            // Arrange
            var service = new MinIoService(_mockOptions.Object, _mockLogger.Object);

            // Act & Assert
            var exception = await Assert.ThrowsAsync<MinIoException>(() => service.ObjectExistsAsync(bucketName, "test-object"));
            Assert.Contains("Bucket name cannot be empty", exception.Message);
        }

        // Note: Null parameter validation tests are not included here because
        // the MinIO service validates bucket existence before parameter validation,
        // which requires a MinIO server connection. These would be integration tests.

        [Fact]
        public void MinIoService_ShouldImplementIDisposable()
        {
            // Arrange & Act
            var service = new MinIoService(_mockOptions.Object, _mockLogger.Object);

            // Assert
            Assert.IsAssignableFrom<IDisposable>(service);

            // Act - Should not throw
            service.Dispose();
        }

        [Fact]
        public void MinIoService_Dispose_ShouldBeCallableMultipleTimes()
        {
            // Arrange
            var service = new MinIoService(_mockOptions.Object, _mockLogger.Object);

            // Act & Assert - Should not throw
            service.Dispose();
            service.Dispose();
            service.Dispose();
        }

        [Theory]
        [InlineData("", "access-key", "secret-key")]
        [InlineData(null, "access-key", "secret-key")]
        [InlineData("localhost:9000", "", "secret-key")]
        [InlineData("localhost:9000", null, "secret-key")]
        [InlineData("localhost:9000", "access-key", "")]
        [InlineData("localhost:9000", "access-key", null)]
        public void MinIoService_Constructor_ShouldThrowArgumentException_WhenSettingsAreInvalid(
            string endpoint, string accessKey, string secretKey)
        {
            // Arrange
            var invalidSettings = new MinIoSettings
            {
                Endpoint = endpoint,
                AccessKey = accessKey,
                SecretKey = secretKey,
                UseSSL = false,
                CreateBucketIfNotExists = true
            };

            // Act & Assert
            Assert.Throws<ArgumentException>(() => new MinIoService(invalidSettings, _mockLogger.Object));
        }

        [Fact]
        public void MinIoService_Constructor_ShouldCreateInstance_WithValidSettings()
        {
            // Arrange
            var validSettings = new MinIoSettings
            {
                Endpoint = "localhost:9000",
                AccessKey = "test-access-key",
                SecretKey = "test-secret-key",
                UseSSL = false,
                CreateBucketIfNotExists = true
            };

            // Act
            var service = new MinIoService(validSettings, _mockLogger.Object);

            // Assert
            Assert.NotNull(service);
        }

        [Fact]
        public async Task DeleteObjectsAsync_ShouldThrowMinIoException_WhenObjectNamesIsNull()
        {
            // Arrange
            var service = new MinIoService(_mockOptions.Object, _mockLogger.Object);

            // Act & Assert
            var exception = await Assert.ThrowsAsync<MinIoException>(() =>
                service.DeleteObjectsAsync("test-bucket", null!));
            Assert.Contains("Value cannot be null", exception.Message);
        }

        [Fact]
        public async Task DeleteObjectsAsync_ShouldThrowMinIoException_WhenObjectNamesIsEmpty()
        {
            // Arrange
            var service = new MinIoService(_mockOptions.Object, _mockLogger.Object);
            var emptyObjectNames = new string[0];

            // Act & Assert
            var exception = await Assert.ThrowsAsync<MinIoException>(() =>
                service.DeleteObjectsAsync("test-bucket", emptyObjectNames));
            Assert.Contains("Please assign list of object names", exception.Message);
        }

        [Theory]
        [InlineData(4 * 1024 * 1024)] // 4MB - below minimum
        [InlineData(6L * 1024 * 1024 * 1024)] // 6GB - above maximum
        public async Task UploadLargeObjectAsync_ShouldThrowMinIoException_WhenPartSizeIsInvalid(long partSize)
        {
            // Arrange
            var service = new MinIoService(_mockOptions.Object, _mockLogger.Object);
            using var stream = new MemoryStream(new byte[1024]);

            // Act & Assert
            var exception = await Assert.ThrowsAsync<MinIoException>(() =>
                service.UploadLargeObjectAsync("test-bucket", "test-object", stream, 1024, partSize: partSize));
            Assert.Contains("Part size", exception.Message);
        }

        #endregion

        #region Helper Methods for Testing

        private static byte[] GenerateRandomBytes(int size)
        {
            var random = new Random();
            var bytes = new byte[size];
            random.NextBytes(bytes);
            return bytes;
        }

        private static string GenerateRandomString(int length)
        {
            const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
            var random = new Random();
            return new string(Enumerable.Repeat(chars, length)
                .Select(s => s[random.Next(s.Length)]).ToArray());
        }

        #endregion
    }
}
