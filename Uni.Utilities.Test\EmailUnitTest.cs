using Microsoft.Extensions.Configuration;
using UNI.Utilities.Email;
using UNI.Utilities.Email.Models;
using Uni.Utilities.Test.Fixtures;

namespace Uni.Utilities.Test
{
    public class EmailUnitTest : IClassFixture<ServiceProviderFixture>
    {
        private readonly ServiceProviderFixture _providerFixture;
        private readonly Settings _settings;
        private readonly IEmailService _emailService;
        public EmailUnitTest(ServiceProviderFixture providerFixture)
        {
            _providerFixture = providerFixture;
            _settings = _providerFixture.Configuration.GetSection("UniEmail").Get<Settings>();
            _emailService = providerFixture.GetService<IEmailService>();
        }

        [Fact(Skip = "skip")]
        public void LoadConfig_Ok()
        {
            Assert.Multiple(() => string.Equals("USEE", _settings.AppName), () => string.Equals("mg.unicloudgroup.com.vn", _settings.Domain));
        }
        [Fact(Skip = "skip")]
        public async Task Send_Ok()
        {

            var emailRequest = new EmailRequest()
            {
                Content = $"TEST from {nameof(EmailUnitTest)}: {nameof(Send_Ok)}",
                Domain = _settings.Domain,
                Subject = nameof(EmailUnitTest),
                From = ["<EMAIL>"],
                To = ["<EMAIL>"]
            };
            var rs = await _emailService.SendAsync(emailRequest);
            Assert.True(rs.Success);

        }
    }
}