using UNI.Utilities.FlexCel;
using UNI.Utilities.FlexCel.Models;

namespace Uni.Utilities.Test
{
    public class ExcelUtilitiesTests
    {
        private readonly DynamicTemplate _template;
        private IPageExportToExcel _exportToExcel;
        public ExcelUtilitiesTests()
        {
            _exportToExcel = new PageExportToExcel();
            _template = new DynamicTemplate();
        }

        [Fact(Skip = "skip")]
        public void BuildTemplate_Ok()
        {
            var source = "Assets/YAMAHA_bao_cao_chuyen_vien_trong_thang.xls";
            var fs = File.OpenRead(source);
            var headers = new List<Header>
            {
                new Header("ColA", "Cột A"),
                new Header("ColB", "Cột B"),
                new Header("ColC", "Cột C"),
            };
            _template.Headers = new List<HeaderData>
            {
                new HeaderData()
                {
                    Id = 1,
                    Headers = headers
                }
            };
            using var mm = new MemoryStream();
            _template.Build(fs, mm);
            File.WriteAllBytes("YAMAHA_bao_cao_chuyen_vien_trong_thang.xls", mm.ToArray());
            Assert.True(mm.ToArray().Length > 0);
        }

        [Fact(Skip = "skip")]
        public async Task PageExportToExcel_Ok()
        {
            var headers = new List<Header>
            {
                new Header("ColA", "Cột A"),
                new Header("ColB", "Cột B"),
                new Header("ColC", "Cột C"),
            };
            var data = new List<object>()
            {
                new { ColA = "Giá trị cột A R1",ColB = "Giá trị cột B R1",ColC = "Giá trị cột C R1"},
                new { ColA = "Giá trị cột A R2",ColB = "Giá trị cột B R2",ColC = "Giá trị cột C R2"},
                new { ColA = "Giá trị cột A R3",ColB = "Giá trị cột B R3",ColC = "Giá trị cột C R3"},
                new { ColA = "Giá trị cột A R4",ColB = "Giá trị cột B R4",ColC = "Giá trị cột C R4",ColD = "Giá trị cột D"},
            };
            var row = data.Count;
            for (int i = 1; i <= 1000000; i++)
            {
                data.Add(new { ColA = $"Giá trị cột A R{row + i}", ColB = $"Giá trị cột B R{row + i}", ColC = $"Giá trị cột C R{row + i}" });
            }
            var exportData = new ExportData()
            {
                Headers = headers,
                Data = data
            };
            var oPath = "test.xlsx";
            var fs = File.Create(oPath);
            fs.Position = 0;
            _exportToExcel.ExportToExcel(exportData, fs);
            fs.Close();
            await fs.DisposeAsync();
            Assert.NotNull(fs);
        }
    }
}