using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using UNI.Utilities.JsonExtension;
using UNI.Utilities.Keycloak.Models.Users;
using UNI.Utils;

namespace Uni.Utilities.Test
{
    public class JsonExtensionTests
    {
        [Fact]
        public void ToObject_Ok()
        {
            const string jsonString =
                "[\r\n  {\r\n    \"id\": \"db34a19b-60e2-45ad-9d24-284bad055cbb\",\r\n    \"createdTimestamp\": 0,\r\n    \"username\": \"0397078692\",\r\n    \"enabled\": true,\r\n    \"totp\": false,\r\n    \"emailVerified\": false,\r\n    \"disableableCredentialTypes\": [],\r\n    \"requiredActions\": [],\r\n    \"notBefore\": 0,\r\n    \"access\": {\r\n      \"manageGroupMembership\": true,\r\n      \"view\": true,\r\n      \"mapRoles\": true,\r\n      \"impersonate\": false,\r\n      \"manage\": true\r\n    }\r\n  },\r\n  {\r\n    \"id\": \"69cdcbfa-b56d-41d2-ba0a-6790e208abe5\",\r\n    \"createdTimestamp\": 0,\r\n    \"username\": \"0886038654\",\r\n    \"enabled\": true,\r\n    \"totp\": false,\r\n    \"emailVerified\": false,\r\n    \"disableableCredentialTypes\": [],\r\n    \"requiredActions\": [],\r\n    \"notBefore\": 0,\r\n    \"access\": {\r\n      \"manageGroupMembership\": true,\r\n      \"view\": true,\r\n      \"mapRoles\": true,\r\n      \"impersonate\": false,\r\n      \"manage\": true\r\n    }\r\n  },\r\n  {\r\n    \"id\": \"1b0cf4f2-438c-410e-ae7b-caee084f3d9a\",\r\n    \"createdTimestamp\": 0,\r\n    \"username\": \"0912345678\",\r\n    \"enabled\": true,\r\n    \"totp\": false,\r\n    \"emailVerified\": false,\r\n    \"lastName\": \"Tran Anh\",\r\n    \"email\": \"<EMAIL>\",\r\n    \"disableableCredentialTypes\": [],\r\n    \"requiredActions\": [],\r\n    \"notBefore\": 0,\r\n    \"access\": {\r\n      \"manageGroupMembership\": true,\r\n      \"view\": true,\r\n      \"mapRoles\": true,\r\n      \"impersonate\": false,\r\n      \"manage\": true\r\n    }\r\n  },\r\n  {\r\n    \"id\": \"40bbec59-d3a4-44b1-83a1-e26e26bc6e24\",\r\n    \"createdTimestamp\": 0,\r\n    \"username\": \"0941332492\",\r\n    \"enabled\": true,\r\n    \"totp\": false,\r\n    \"emailVerified\": false,\r\n    \"disableableCredentialTypes\": [],\r\n    \"requiredActions\": [],\r\n    \"notBefore\": 0,\r\n    \"access\": {\r\n      \"manageGroupMembership\": true,\r\n      \"view\": true,\r\n      \"mapRoles\": true,\r\n      \"impersonate\": false,\r\n      \"manage\": true\r\n    }\r\n  },\r\n  {\r\n    \"id\": \"d3966bb4-15b7-477c-905d-c40e3e682387\",\r\n    \"createdTimestamp\": 0,\r\n    \"username\": \"0963100897\",\r\n    \"enabled\": true,\r\n    \"totp\": false,\r\n    \"emailVerified\": false,\r\n    \"disableableCredentialTypes\": [],\r\n    \"requiredActions\": [],\r\n    \"notBefore\": 0,\r\n    \"access\": {\r\n      \"manageGroupMembership\": true,\r\n      \"view\": true,\r\n      \"mapRoles\": true,\r\n      \"impersonate\": false,\r\n      \"manage\": true\r\n    }\r\n  },\r\n  {\r\n    \"id\": \"154cee91-a811-4a13-ab5e-5f7145e960d8\",\r\n    \"createdTimestamp\": 0,\r\n    \"username\": \"0979060822\",\r\n    \"enabled\": true,\r\n    \"totp\": false,\r\n    \"emailVerified\": false,\r\n    \"disableableCredentialTypes\": [],\r\n    \"requiredActions\": [],\r\n    \"notBefore\": 0,\r\n    \"access\": {\r\n      \"manageGroupMembership\": true,\r\n      \"view\": true,\r\n      \"mapRoles\": true,\r\n      \"impersonate\": false,\r\n      \"manage\": true\r\n    }\r\n  },\r\n  {\r\n    \"id\": \"7f386a01-2918-4f4f-89e3-d05fde759037\",\r\n    \"createdTimestamp\": 0,\r\n    \"username\": \"0982726281\",\r\n    \"enabled\": true,\r\n    \"totp\": false,\r\n    \"emailVerified\": false,\r\n    \"disableableCredentialTypes\": [],\r\n    \"requiredActions\": [],\r\n    \"notBefore\": 0,\r\n    \"access\": {\r\n      \"manageGroupMembership\": true,\r\n      \"view\": true,\r\n      \"mapRoles\": true,\r\n      \"impersonate\": false,\r\n      \"manage\": true\r\n    }\r\n  },\r\n  {\r\n    \"id\": \"26c49a18-02dc-4020-b8a5-3f0791d13afb\",\r\n    \"createdTimestamp\": 0,\r\n    \"username\": \"0988279029\",\r\n    \"enabled\": true,\r\n    \"totp\": false,\r\n    \"emailVerified\": false,\r\n    \"disableableCredentialTypes\": [],\r\n    \"requiredActions\": [],\r\n    \"notBefore\": 0,\r\n    \"access\": {\r\n      \"manageGroupMembership\": true,\r\n      \"view\": true,\r\n      \"mapRoles\": true,\r\n      \"impersonate\": false,\r\n      \"manage\": true\r\n    }\r\n  },\r\n  {\r\n    \"id\": \"f9573ada-966e-4a58-99ab-faef7bfb7b56\",\r\n    \"createdTimestamp\": 0,\r\n    \"username\": \"0988686022\",\r\n    \"enabled\": true,\r\n    \"totp\": false,\r\n    \"emailVerified\": false,\r\n    \"disableableCredentialTypes\": [],\r\n    \"requiredActions\": [],\r\n    \"notBefore\": 0,\r\n    \"access\": {\r\n      \"manageGroupMembership\": true,\r\n      \"view\": true,\r\n      \"mapRoles\": true,\r\n      \"impersonate\": false,\r\n      \"manage\": true\r\n    }\r\n  },\r\n  {\r\n    \"id\": \"fe33fe71-bc5e-4ac6-ae2f-709faacda8e2\",\r\n    \"createdTimestamp\": 1732249622404,\r\n    \"username\": \"demo\",\r\n    \"enabled\": true,\r\n    \"totp\": false,\r\n    \"emailVerified\": true,\r\n    \"firstName\": \"Demo\",\r\n    \"disableableCredentialTypes\": [],\r\n    \"requiredActions\": [],\r\n    \"notBefore\": 0,\r\n    \"access\": {\r\n      \"manageGroupMembership\": true,\r\n      \"view\": true,\r\n      \"mapRoles\": true,\r\n      \"impersonate\": false,\r\n      \"manage\": true\r\n    }\r\n  },\r\n  {\r\n    \"id\": \"64c3e5cd-c831-4e76-ac33-39570f52e4a9\",\r\n    \"createdTimestamp\": 1732015356164,\r\n    \"username\": \"manhng\",\r\n    \"enabled\": true,\r\n    \"totp\": false,\r\n    \"emailVerified\": false,\r\n    \"disableableCredentialTypes\": [],\r\n    \"requiredActions\": [],\r\n    \"notBefore\": 0,\r\n    \"access\": {\r\n      \"manageGroupMembership\": true,\r\n      \"view\": true,\r\n      \"mapRoles\": true,\r\n      \"impersonate\": false,\r\n      \"manage\": true\r\n    }\r\n  }\r\n]\r\n";
            var users = jsonString.FromJson<List<User>>();
            Assert.True(users?.Any());
        }
    }
}
